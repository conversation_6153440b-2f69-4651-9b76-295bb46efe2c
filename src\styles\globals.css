@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    /* 主色调变量 */
    --primary-50: 243 241 255;
    --primary-100: 235 229 255;
    --primary-200: 217 206 255;
    --primary-300: 190 166 255;
    --primary-400: 159 117 255;
    --primary-500: 132 61 255;
    --primary-600: 121 22 255;
    --primary-700: 107 4 253;
    --primary-800: 90 3 212;
    --primary-900: 75 5 173;
    --primary-950: 44 0 118;

    /* 辅助色变量 */
    --accent-50: 255 251 235;
    --accent-100: 254 243 199;
    --accent-200: 253 230 138;
    --accent-300: 252 211 77;
    --accent-400: 251 191 36;
    --accent-500: 245 158 11;
    --accent-600: 217 119 6;
    --accent-700: 180 83 9;
    --accent-800: 146 64 14;
    --accent-900: 120 53 15;
    --accent-950: 69 26 3;

    /* 神秘蓝色变量 */
    --mystical-50: 239 246 255;
    --mystical-100: 219 234 254;
    --mystical-200: 191 219 254;
    --mystical-300: 147 197 253;
    --mystical-400: 96 165 250;
    --mystical-500: 59 130 246;
    --mystical-600: 37 99 235;
    --mystical-700: 29 78 216;
    --mystical-800: 30 64 175;
    --mystical-900: 30 58 138;
    --mystical-950: 23 37 84;

    /* 默认浅色主题变量 */
    --background: 255 255 255;
    --background-secondary: 248 250 252;
    --background-tertiary: 241 245 249;

    --foreground: 15 23 42;
    --foreground-secondary: 51 65 85;
    --foreground-muted: 100 116 139;

    --border: 226 232 240;
    --border-secondary: 203 213 225;

    --card: 255 255 255;
    --card-secondary: 248 250 252;
  }

  .dark {
    /* 深色主题变量 */
    --background: 10 10 15;
    --background-secondary: 26 26 46;
    --background-tertiary: 22 33 62;

    --foreground: 255 255 255;
    --foreground-secondary: 226 232 240;
    --foreground-muted: 148 163 184;

    --border: 51 65 85;
    --border-secondary: 71 85 105;

    --card: 30 41 59;
    --card-secondary: 51 65 85;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans transition-colors duration-300;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 确保主题切换的平滑过渡 */
  * {
    transition-property: background-color, border-color, color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-border-secondary;
  }

  /* 选择文本样式 */
  ::selection {
    @apply bg-primary-500/20 text-primary-900;
  }

  /* 焦点样式 */
  :focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-background;
  }
}

@layer components {
  /* 神秘主题组件样式 */
  .mystical-card {
    @apply bg-gradient-to-br from-card to-card/80 border border-primary/20 shadow-mystical backdrop-blur-sm;
  }

  .golden-card {
    @apply bg-gradient-to-br from-card to-accent-500/5 border border-accent-500/20 shadow-golden backdrop-blur-sm;
  }

  .glass-card {
    @apply bg-white/5 border border-white/10 backdrop-blur-md shadow-lg;
  }

  /* 渐变文本 */
  .text-mystical-gradient {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent;
  }

  .text-golden-gradient {
    @apply bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent;
  }

  /* 神秘按钮效果 */
  .btn-mystical {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-mystical hover:shadow-mystical-lg transition-all duration-300 hover:scale-105;
  }

  .btn-golden {
    @apply bg-gradient-to-r from-accent-500 to-accent-600 text-white shadow-golden hover:shadow-golden-lg transition-all duration-300 hover:scale-105;
  }

  /* 动画效果 */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    animation: shimmer 2s linear infinite;
  }

  /* 星空背景 */
  .star-field {
    background-image: 
      radial-gradient(2px 2px at 20px 30px, #eee, transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
      radial-gradient(1px 1px at 90px 40px, #fff, transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
      radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
  }

  /* 响应式容器 */
  .container-responsive {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 文本样式 */
  .text-balance {
    text-wrap: balance;
  }

  /* 多语言字体支持 */
  .font-chinese {
    font-family: 'Noto Sans SC', 'PingFang SC', sans-serif;
  }

  .font-japanese {
    font-family: 'Noto Sans JP', sans-serif;
  }

  .font-arabic {
    font-family: 'Noto Sans Arabic', sans-serif;
    direction: rtl;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-500;
  }

  /* 卡片悬停效果 */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.02];
  }

  /* 神秘光效 */
  .mystical-glow {
    box-shadow: 
      0 0 20px rgba(132, 61, 255, 0.3),
      0 0 40px rgba(132, 61, 255, 0.2),
      0 0 60px rgba(132, 61, 255, 0.1);
  }

  .golden-glow {
    box-shadow: 
      0 0 20px rgba(245, 158, 11, 0.3),
      0 0 40px rgba(245, 158, 11, 0.2),
      0 0 60px rgba(245, 158, 11, 0.1);
  }
}

@layer utilities {
  /* 隐藏滚动条但保持功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 文本省略 */
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 安全区域适配 */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}
