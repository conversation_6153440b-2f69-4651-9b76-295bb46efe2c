"use client";var N=Object.create;var P=Object.defineProperty;var O=Object.getOwnPropertyDescriptor;var U=Object.getOwnPropertyNames;var _=Object.getPrototypeOf,j=Object.prototype.hasOwnProperty;var z=(e,n)=>{for(var s in n)P(e,s,{get:n[s],enumerable:!0})},T=(e,n,s,u)=>{if(n&&typeof n=="object"||typeof n=="function")for(let r of U(n))!j.call(e,r)&&r!==s&&P(e,r,{get:()=>n[r],enumerable:!(u=O(n,r))||u.enumerable});return e};var J=(e,n,s)=>(s=e!=null?N(_(e)):{},T(n||!e||!e.__esModule?P(s,"default",{value:e,enumerable:!0}):s,e)),V=e=>T(P({},"__esModule",{value:!0}),e);var Y={};z(Y,{ThemeProvider:()=>B,useTheme:()=>q});module.exports=V(Y);var t=J(require("react")),C=["light","dark"],L="(prefers-color-scheme: dark)",H=typeof window=="undefined",M=t.createContext(void 0),b={setTheme:e=>{},themes:[]},q=()=>{var e;return(e=t.useContext(M))!=null?e:b},B=e=>t.useContext(M)?e.children:t.createElement(G,{...e}),F=["light","dark"],G=({forcedTheme:e,disableTransitionOnChange:n=!1,enableSystem:s=!0,enableColorScheme:u=!0,storageKey:r="theme",themes:a=F,defaultTheme:c=s?"system":"light",attribute:g="data-theme",value:p,children:k,nonce:w})=>{let[d,l]=t.useState(()=>I(r,c)),[S,m]=t.useState(()=>I(r)),f=p?Object.values(p):a,R=t.useCallback(o=>{let i=o;if(!i)return;o==="system"&&s&&(i=A());let y=p?p[i]:i,E=n?X():null,x=document.documentElement;if(g==="class"?(x.classList.remove(...f),y&&x.classList.add(y)):y?x.setAttribute(g,y):x.removeAttribute(g),u){let Q=C.includes(c)?c:null,D=C.includes(i)?i:Q;x.style.colorScheme=D}E==null||E()},[]),h=t.useCallback(o=>{let i=typeof o=="function"?o(o):o;l(i);try{localStorage.setItem(r,i)}catch(y){}},[e]),$=t.useCallback(o=>{let i=A(o);m(i),d==="system"&&s&&!e&&R("system")},[d,e]);t.useEffect(()=>{let o=window.matchMedia(L);return o.addListener($),$(o),()=>o.removeListener($)},[$]),t.useEffect(()=>{let o=i=>{if(i.key!==r)return;let y=i.newValue||c;h(y)};return window.addEventListener("storage",o),()=>window.removeEventListener("storage",o)},[h]),t.useEffect(()=>{R(e!=null?e:d)},[e,d]);let v=t.useMemo(()=>({theme:d,setTheme:h,forcedTheme:e,resolvedTheme:d==="system"?S:d,themes:s?[...a,"system"]:a,systemTheme:s?S:void 0}),[d,h,e,S,s,a]);return t.createElement(M.Provider,{value:v},t.createElement(W,{forcedTheme:e,disableTransitionOnChange:n,enableSystem:s,enableColorScheme:u,storageKey:r,themes:a,defaultTheme:c,attribute:g,value:p,children:k,attrs:f,nonce:w}),k)},W=t.memo(({forcedTheme:e,storageKey:n,attribute:s,enableSystem:u,enableColorScheme:r,defaultTheme:a,value:c,attrs:g,nonce:p})=>{let k=a==="system",w=s==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${g.map(f=>`'${f}'`).join(",")})`};`:`var d=document.documentElement,n='${s}',s='setAttribute';`,d=r?(C.includes(a)?a:null)?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${a}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",l=(m,f=!1,R=!0)=>{let h=c?c[m]:m,$=f?m+"|| ''":`'${h}'`,v="";return r&&R&&!f&&C.includes(m)&&(v+=`d.style.colorScheme = '${m}';`),s==="class"?f||h?v+=`c.add(${$})`:v+="null":h&&(v+=`d[s](n,${$})`),v},S=e?`!function(){${w}${l(e)}}()`:u?`!function(){try{${w}var e=localStorage.getItem('${n}');if('system'===e||(!e&&${k})){var t='${L}',m=window.matchMedia(t);if(m.media!==t||m.matches){${l("dark")}}else{${l("light")}}}else if(e){${c?`var x=${JSON.stringify(c)};`:""}${l(c?"x[e]":"e",!0)}}${k?"":"else{"+l(a,!1,!1)+"}"}${d}}catch(e){}}()`:`!function(){try{${w}var e=localStorage.getItem('${n}');if(e){${c?`var x=${JSON.stringify(c)};`:""}${l(c?"x[e]":"e",!0)}}else{${l(a,!1,!1)};}${d}}catch(t){}}();`;return t.createElement("script",{nonce:p,dangerouslySetInnerHTML:{__html:S}})}),I=(e,n)=>{if(H)return;let s;try{s=localStorage.getItem(e)||void 0}catch(u){}return s||n},X=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},A=e=>(e||(e=window.matchMedia(L)),e.matches?"dark":"light");0&&(module.exports={ThemeProvider,useTheme});
