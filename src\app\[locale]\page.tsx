import { useTranslations } from 'next-intl';
import { Metadata } from 'next';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';

interface HomePageProps {
  params: { locale: string };
}

export async function generateMetadata({ params: { locale } }: HomePageProps): Promise<Metadata> {
  return generateSEOMetadata({
    title: 'Discover Your Mystical Code - Free AI-Powered Tests',
    description: 'Professional AI analysis for accurate insights into your personality and destiny through tarot, astrology, and numerology tests.',
    keywords: ['mystical tests', 'free tests', 'AI analysis', 'tarot', 'astrology', 'numerology'],
    locale,
    type: 'website',
  });
}

export default function HomePage({ params: { locale } }: HomePageProps) {
  const t = useTranslations('homepage');

  const featuredTests = [
    {
      title: 'Tarot Reading',
      description: 'Unlock the wisdom of the cards and discover insights about your past, present, and future.',
      href: '/tarot/test',
      icon: '🔮',
    },
    {
      title: 'Astrology Analysis',
      description: 'Explore the influence of celestial bodies on your personality and life path.',
      href: '/astrology/test',
      icon: '⭐',
    },
    {
      title: 'Numerology Calculator',
      description: 'Decode the power of numbers and reveal your life path through mystical mathematics.',
      href: '/numerology/test',
      icon: '🔢',
    },
    {
      title: 'Crystal Energy',
      description: 'Discover which crystals resonate with your energy and can enhance your spiritual journey.',
      href: '/crystal/test',
      icon: '💎',
    },
    {
      title: 'Palm Reading',
      description: 'Analyze the lines and shapes of your hands to understand your character and destiny.',
      href: '/palmistry/test',
      icon: '✋',
    },
    {
      title: 'Dream Interpretation',
      description: 'Uncover the hidden meanings in your dreams and their significance to your waking life.',
      href: '/dreams/test',
      icon: '🌙',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-background via-background-secondary to-background-tertiary">
        <div className="absolute inset-0 star-field opacity-20 dark:opacity-30"></div>
        <div className="relative container-responsive py-24 lg:py-32">
          <div className="text-center max-w-4xl mx-auto">

            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-mystical-gradient animate-fade-in">
              {t('title')}
            </h1>
            <p className="text-xl md:text-2xl text-foreground-secondary mb-8 animate-fade-up">
              {t('subtitle')}
            </p>
            <Button
              variant="mystical"
              size="xl"
              className="animate-scale-in"
            >
              {t('cta')}
            </Button>
          </div>
        </div>
      </section>

      {/* Featured Tests Section */}
      <section className="py-20 bg-background-secondary/30 dark:bg-background-secondary/50">
        <div className="container-responsive">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-golden-gradient">
              {t('featuredTests')}
            </h2>
            <p className="text-lg text-foreground-secondary max-w-2xl mx-auto">
              Choose from our collection of mystical tests powered by advanced AI analysis
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredTests.map((test, index) => (
              <Card 
                key={test.title} 
                variant="mystical" 
                hover
                className="animate-fade-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <CardHeader className="text-center">
                  <div className="text-4xl mb-4">{test.icon}</div>
                  <CardTitle className="text-xl">{test.title}</CardTitle>
                  <CardDescription>{test.description}</CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <Button variant="outline" className="w-full">
                    Start Test
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-background">
        <div className="container-responsive">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="animate-fade-up">
              <div className="text-4xl md:text-5xl font-bold text-mystical-gradient mb-2">
                100K+
              </div>
              <p className="text-foreground-secondary">{t('stats.users')}</p>
            </div>
            <div className="animate-fade-up" style={{ animationDelay: '0.1s' }}>
              <div className="text-4xl md:text-5xl font-bold text-golden-gradient mb-2">
                95%
              </div>
              <p className="text-foreground-secondary">{t('stats.accuracy')}</p>
            </div>
            <div className="animate-fade-up" style={{ animationDelay: '0.2s' }}>
              <div className="text-4xl md:text-5xl font-bold text-mystical-gradient mb-2">
                6
              </div>
              <p className="text-foreground-secondary">{t('stats.languages')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-500/10 to-accent-500/10">
        <div className="container-responsive text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Discover Your Mystical Path?
          </h2>
          <p className="text-lg text-foreground-secondary mb-8 max-w-2xl mx-auto">
            Join thousands of users who have already unlocked their spiritual insights with our AI-powered mystical tests.
          </p>
          <Button variant="mystical" size="xl">
            Start Your Journey
          </Button>
        </div>
      </section>
    </div>
  );
}
